// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class Null_State_1 : ModuleRules
{
	public Null_State_1(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] {
			"Core",
			"CoreUObject",
			"Engine",
			"InputCore",
			"EnhancedInput",
			"AIModule",
			"StateTreeModule",
			"GameplayStateTreeModule",
			"UMG"
		});

		PrivateDependencyModuleNames.AddRange(new string[] { });

		PublicIncludePaths.AddRange(new string[] {
			"Null_State_1",
			"Null_State_1/Variant_Platforming",
			"Null_State_1/Variant_Combat",
			"Null_State_1/Variant_Combat/AI",
			"Null_State_1/Variant_SideScrolling",
			"Null_State_1/Variant_SideScrolling/Gameplay",
			"Null_State_1/Variant_SideScrolling/AI"
		});

		// Uncomment if you are using Slate UI
		// PrivateDependencyModuleNames.AddRange(new string[] { "Slate", "SlateCore" });

		// Uncomment if you are using online features
		// PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// To include OnlineSubsystemSteam, add it to the plugins section in your uproject file with the Enabled attribute set to true
	}
}
