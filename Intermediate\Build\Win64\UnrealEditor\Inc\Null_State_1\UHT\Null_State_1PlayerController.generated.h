// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Null_State_1PlayerController.h"

#ifdef NULL_STATE_1_Null_State_1PlayerController_generated_h
#error "Null_State_1PlayerController.generated.h already included, missing '#pragma once' in Null_State_1PlayerController.h"
#endif
#define NULL_STATE_1_Null_State_1PlayerController_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class ANull_State_1PlayerController ********************************************
NULL_STATE_1_API UClass* Z_Construct_UClass_ANull_State_1PlayerController_NoRegister();

#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1PlayerController_h_18_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesANull_State_1PlayerController(); \
	friend struct Z_Construct_UClass_ANull_State_1PlayerController_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend NULL_STATE_1_API UClass* Z_Construct_UClass_ANull_State_1PlayerController_NoRegister(); \
public: \
	DECLARE_CLASS2(ANull_State_1PlayerController, APlayerController, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Config), CASTCLASS_None, TEXT("/Script/Null_State_1"), Z_Construct_UClass_ANull_State_1PlayerController_NoRegister) \
	DECLARE_SERIALIZER(ANull_State_1PlayerController)


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1PlayerController_h_18_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API ANull_State_1PlayerController(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	ANull_State_1PlayerController(ANull_State_1PlayerController&&) = delete; \
	ANull_State_1PlayerController(const ANull_State_1PlayerController&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ANull_State_1PlayerController); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ANull_State_1PlayerController); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(ANull_State_1PlayerController) \
	NO_API virtual ~ANull_State_1PlayerController();


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1PlayerController_h_15_PROLOG
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1PlayerController_h_18_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1PlayerController_h_18_INCLASS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1PlayerController_h_18_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ANull_State_1PlayerController;

// ********** End Class ANull_State_1PlayerController **********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1PlayerController_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
