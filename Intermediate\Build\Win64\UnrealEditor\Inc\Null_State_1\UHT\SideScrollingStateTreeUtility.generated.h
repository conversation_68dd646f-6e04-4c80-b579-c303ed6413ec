// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Variant_SideScrolling/AI/SideScrollingStateTreeUtility.h"

#ifdef NULL_STATE_1_SideScrollingStateTreeUtility_generated_h
#error "SideScrollingStateTreeUtility.generated.h already included, missing '#pragma once' in SideScrollingStateTreeUtility.h"
#endif
#define NULL_STATE_1_SideScrollingStateTreeUtility_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FStateTreeGetPlayerInstanceData ***********************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingStateTreeUtility_h_18_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeGetPlayerInstanceData_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct();


struct FStateTreeGetPlayerInstanceData;
// ********** End ScriptStruct FStateTreeGetPlayerInstanceData *************************************

// ********** Begin ScriptStruct FStateTreeGetPlayerTask *******************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingStateTreeUtility_h_47_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeGetPlayerTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeGetPlayerTask;
// ********** End ScriptStruct FStateTreeGetPlayerTask *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingStateTreeUtility_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
