// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Variant_Combat/AnimNotify_DoAttackTrace.h"

#ifdef NULL_STATE_1_AnimNotify_DoAttackTrace_generated_h
#error "AnimNotify_DoAttackTrace.generated.h already included, missing '#pragma once' in AnimNotify_DoAttackTrace.h"
#endif
#define NULL_STATE_1_AnimNotify_DoAttackTrace_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UAnimNotify_DoAttackTrace ************************************************
NULL_STATE_1_API UClass* Z_Construct_UClass_UAnimNotify_DoAttackTrace_NoRegister();

#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_DoAttackTrace_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAnimNotify_DoAttackTrace(); \
	friend struct Z_Construct_UClass_UAnimNotify_DoAttackTrace_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend NULL_STATE_1_API UClass* Z_Construct_UClass_UAnimNotify_DoAttackTrace_NoRegister(); \
public: \
	DECLARE_CLASS2(UAnimNotify_DoAttackTrace, UAnimNotify, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/Null_State_1"), Z_Construct_UClass_UAnimNotify_DoAttackTrace_NoRegister) \
	DECLARE_SERIALIZER(UAnimNotify_DoAttackTrace)


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_DoAttackTrace_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAnimNotify_DoAttackTrace(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAnimNotify_DoAttackTrace(UAnimNotify_DoAttackTrace&&) = delete; \
	UAnimNotify_DoAttackTrace(const UAnimNotify_DoAttackTrace&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAnimNotify_DoAttackTrace); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAnimNotify_DoAttackTrace); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAnimNotify_DoAttackTrace) \
	NO_API virtual ~UAnimNotify_DoAttackTrace();


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_DoAttackTrace_h_12_PROLOG
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_DoAttackTrace_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_DoAttackTrace_h_15_INCLASS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_DoAttackTrace_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAnimNotify_DoAttackTrace;

// ********** End Class UAnimNotify_DoAttackTrace **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_DoAttackTrace_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
