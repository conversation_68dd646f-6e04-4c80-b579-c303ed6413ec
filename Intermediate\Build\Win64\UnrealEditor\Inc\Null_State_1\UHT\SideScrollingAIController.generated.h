// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Variant_SideScrolling/AI/SideScrollingAIController.h"

#ifdef NULL_STATE_1_SideScrollingAIController_generated_h
#error "SideScrollingAIController.generated.h already included, missing '#pragma once' in SideScrollingAIController.h"
#endif
#define NULL_STATE_1_SideScrollingAIController_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class ASideScrollingAIController ***********************************************
NULL_STATE_1_API UClass* Z_Construct_UClass_ASideScrollingAIController_NoRegister();

#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingAIController_h_17_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesASideScrollingAIController(); \
	friend struct Z_Construct_UClass_ASideScrollingAIController_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend NULL_STATE_1_API UClass* Z_Construct_UClass_ASideScrollingAIController_NoRegister(); \
public: \
	DECLARE_CLASS2(ASideScrollingAIController, AAIController, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Config), CASTCLASS_None, TEXT("/Script/Null_State_1"), Z_Construct_UClass_ASideScrollingAIController_NoRegister) \
	DECLARE_SERIALIZER(ASideScrollingAIController)


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingAIController_h_17_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ASideScrollingAIController(ASideScrollingAIController&&) = delete; \
	ASideScrollingAIController(const ASideScrollingAIController&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ASideScrollingAIController); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ASideScrollingAIController); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(ASideScrollingAIController) \
	NO_API virtual ~ASideScrollingAIController();


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingAIController_h_14_PROLOG
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingAIController_h_17_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingAIController_h_17_INCLASS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingAIController_h_17_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ASideScrollingAIController;

// ********** End Class ASideScrollingAIController *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_AI_SideScrollingAIController_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
