// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Variant_Combat/AnimNotify_CheckChargedAttack.h"

#ifdef NULL_STATE_1_AnimNotify_CheckChargedAttack_generated_h
#error "AnimNotify_CheckChargedAttack.generated.h already included, missing '#pragma once' in AnimNotify_CheckChargedAttack.h"
#endif
#define NULL_STATE_1_AnimNotify_CheckChargedAttack_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UAnimNotify_CheckChargedAttack *******************************************
NULL_STATE_1_API UClass* Z_Construct_UClass_UAnimNotify_CheckChargedAttack_NoRegister();

#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_CheckChargedAttack_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAnimNotify_CheckChargedAttack(); \
	friend struct Z_Construct_UClass_UAnimNotify_CheckChargedAttack_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend NULL_STATE_1_API UClass* Z_Construct_UClass_UAnimNotify_CheckChargedAttack_NoRegister(); \
public: \
	DECLARE_CLASS2(UAnimNotify_CheckChargedAttack, UAnimNotify, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/Null_State_1"), Z_Construct_UClass_UAnimNotify_CheckChargedAttack_NoRegister) \
	DECLARE_SERIALIZER(UAnimNotify_CheckChargedAttack)


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_CheckChargedAttack_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAnimNotify_CheckChargedAttack(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAnimNotify_CheckChargedAttack(UAnimNotify_CheckChargedAttack&&) = delete; \
	UAnimNotify_CheckChargedAttack(const UAnimNotify_CheckChargedAttack&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAnimNotify_CheckChargedAttack); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAnimNotify_CheckChargedAttack); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAnimNotify_CheckChargedAttack) \
	NO_API virtual ~UAnimNotify_CheckChargedAttack();


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_CheckChargedAttack_h_12_PROLOG
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_CheckChargedAttack_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_CheckChargedAttack_h_15_INCLASS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_CheckChargedAttack_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAnimNotify_CheckChargedAttack;

// ********** End Class UAnimNotify_CheckChargedAttack *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AnimNotify_CheckChargedAttack_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
