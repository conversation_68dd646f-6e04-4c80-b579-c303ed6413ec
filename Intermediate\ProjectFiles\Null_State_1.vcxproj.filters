<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <None Include="..\..\Null_State_1.uproject" />
    <Filter Include="Source">
      <UniqueIdentifier>{F31BBDD1-B3E8-3BCC-9652-680E16935819}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Null_State_1.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\Source\Null_State_1Editor.Target.cs">
      <Filter>Source</Filter>
    </None>
    <Filter Include="Config">
      <UniqueIdentifier>{FA535FFB-25E1-3D20-B416-52F9BE21E06E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Config\DefaultEditor.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEditorPerProjectUserSettings.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEngine.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGame.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultInput.ini">
      <Filter>Config</Filter>
    </None>
    <Filter Include="Source\Null_State_1">
      <UniqueIdentifier>{F9C4D614-6B2B-3318-9170-F826DF6BF10B}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Null_State_1\Null_State_1.Build.cs">
      <Filter>Source\Null_State_1</Filter>
    </None>
    <ClCompile Include="..\..\Source\Null_State_1\Null_State_1.cpp">
      <Filter>Source\Null_State_1</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Null_State_1.h">
      <Filter>Source\Null_State_1</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Null_State_1Character.cpp">
      <Filter>Source\Null_State_1</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Null_State_1Character.h">
      <Filter>Source\Null_State_1</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Null_State_1GameMode.cpp">
      <Filter>Source\Null_State_1</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Null_State_1GameMode.h">
      <Filter>Source\Null_State_1</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Null_State_1PlayerController.cpp">
      <Filter>Source\Null_State_1</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Null_State_1PlayerController.h">
      <Filter>Source\Null_State_1</Filter>
    </ClInclude>
    <Filter Include="Source\Null_State_1\Variant_Combat">
      <UniqueIdentifier>{D1842433-83F1-397C-A87B-98381FE70953}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AnimNotify_CheckChargedAttack.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AnimNotify_CheckChargedAttack.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AnimNotify_CheckCombo.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AnimNotify_CheckCombo.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AnimNotify_DoAttackTrace.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AnimNotify_DoAttackTrace.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatActivatable.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatActivatable.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatActivationVolume.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatActivationVolume.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatAttacker.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatAttacker.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatCharacter.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatCharacter.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatCheckpointVolume.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatCheckpointVolume.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatDamageable.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatDamageable.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatDamageableBox.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatDamageableBox.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatDummy.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatDummy.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatGameMode.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatGameMode.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatLavaFloor.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatLavaFloor.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatLifeBar.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatLifeBar.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\CombatPlayerController.cpp">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\CombatPlayerController.h">
      <Filter>Source\Null_State_1\Variant_Combat</Filter>
    </ClInclude>
    <Filter Include="Source\Null_State_1\Variant_Combat\AI">
      <UniqueIdentifier>{10A14E76-52E2-3FAD-AA0A-A3FF47F70D49}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatAIController.cpp">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatAIController.h">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatEnemy.cpp">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatEnemy.h">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatEnemySpawner.cpp">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatEnemySpawner.h">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatStateTreeUtility.cpp">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AI\CombatStateTreeUtility.h">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Combat\AI\EnvQueryContext_Player.cpp">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Combat\AI\EnvQueryContext_Player.h">
      <Filter>Source\Null_State_1\Variant_Combat\AI</Filter>
    </ClInclude>
    <Filter Include="Source\Null_State_1\Variant_Platforming">
      <UniqueIdentifier>{724989B1-14F9-3B59-8925-164D5CFCB93D}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Platforming\AnimNotify_EndDash.cpp">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Platforming\AnimNotify_EndDash.h">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Platforming\PlatformingCharacter.cpp">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Platforming\PlatformingCharacter.h">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Platforming\PlatformingGameMode.cpp">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Platforming\PlatformingGameMode.h">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_Platforming\PlatformingPlayerController.cpp">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_Platforming\PlatformingPlayerController.h">
      <Filter>Source\Null_State_1\Variant_Platforming</Filter>
    </ClInclude>
    <Filter Include="Source\Null_State_1\Variant_SideScrolling">
      <UniqueIdentifier>{FC2AD3D2-DE53-3B10-8099-6ABCA2146F2A}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingCameraManager.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingCameraManager.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingCharacter.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingCharacter.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClInclude>
    <None Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingCPP.Build.cs">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </None>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingGameMode.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingGameMode.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingInteractable.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingInteractable.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingPlayerController.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingPlayerController.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingUI.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\SideScrollingUI.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling</Filter>
    </ClInclude>
    <Filter Include="Source\Null_State_1\Variant_SideScrolling\AI">
      <UniqueIdentifier>{50652008-622B-3938-B60B-A3890D09FBDD}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\AI\SideScrollingAIController.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\AI\SideScrollingAIController.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling\AI</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\AI\SideScrollingNPC.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\AI\SideScrollingNPC.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling\AI</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\AI\SideScrollingStateTreeUtility.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling\AI</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\AI\SideScrollingStateTreeUtility.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling\AI</Filter>
    </ClInclude>
    <Filter Include="Source\Null_State_1\Variant_SideScrolling\Gameplay">
      <UniqueIdentifier>{1B847516-817B-3D8E-9F33-9E94DEFAF90A}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingJumpPad.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingJumpPad.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingMovingPlatform.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingMovingPlatform.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingPickup.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingPickup.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClInclude>
    <ClCompile Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingSoftPlatform.cpp">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Null_State_1\Variant_SideScrolling\Gameplay\SideScrollingSoftPlatform.h">
      <Filter>Source\Null_State_1\Variant_SideScrolling\Gameplay</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
