// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Null_State_1/Null_State_1GameMode.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeNull_State_1GameMode() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_AGameModeBase();
NULL_STATE_1_API UClass* Z_Construct_UClass_ANull_State_1GameMode();
NULL_STATE_1_API UClass* Z_Construct_UClass_ANull_State_1GameMode_NoRegister();
UPackage* Z_Construct_UPackage__Script_Null_State_1();
// ********** End Cross Module References **********************************************************

// ********** Begin Class ANull_State_1GameMode ****************************************************
void ANull_State_1GameMode::StaticRegisterNativesANull_State_1GameMode()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_ANull_State_1GameMode;
UClass* ANull_State_1GameMode::GetPrivateStaticClass()
{
	using TClass = ANull_State_1GameMode;
	if (!Z_Registration_Info_UClass_ANull_State_1GameMode.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("Null_State_1GameMode"),
			Z_Registration_Info_UClass_ANull_State_1GameMode.InnerSingleton,
			StaticRegisterNativesANull_State_1GameMode,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ANull_State_1GameMode.InnerSingleton;
}
UClass* Z_Construct_UClass_ANull_State_1GameMode_NoRegister()
{
	return ANull_State_1GameMode::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ANull_State_1GameMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *  Simple GameMode for a third person game\n */" },
#endif
		{ "HideCategories", "Info Rendering MovementReplication Replication Actor Input Movement Collision Rendering HLOD WorldPartition DataLayers Transformation" },
		{ "IncludePath", "Null_State_1GameMode.h" },
		{ "ModuleRelativePath", "Null_State_1GameMode.h" },
		{ "ShowCategories", "Input|MouseInput Input|TouchInput" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simple GameMode for a third person game" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ANull_State_1GameMode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_ANull_State_1GameMode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AGameModeBase,
	(UObject* (*)())Z_Construct_UPackage__Script_Null_State_1,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ANull_State_1GameMode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ANull_State_1GameMode_Statics::ClassParams = {
	&ANull_State_1GameMode::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x008003ADu,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ANull_State_1GameMode_Statics::Class_MetaDataParams), Z_Construct_UClass_ANull_State_1GameMode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ANull_State_1GameMode()
{
	if (!Z_Registration_Info_UClass_ANull_State_1GameMode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ANull_State_1GameMode.OuterSingleton, Z_Construct_UClass_ANull_State_1GameMode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ANull_State_1GameMode.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ANull_State_1GameMode);
ANull_State_1GameMode::~ANull_State_1GameMode() {}
// ********** End Class ANull_State_1GameMode ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h__Script_Null_State_1_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ANull_State_1GameMode, ANull_State_1GameMode::StaticClass, TEXT("ANull_State_1GameMode"), &Z_Registration_Info_UClass_ANull_State_1GameMode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ANull_State_1GameMode), 4175823519U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h__Script_Null_State_1_1587979516(TEXT("/Script/Null_State_1"),
	Z_CompiledInDeferFile_FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h__Script_Null_State_1_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h__Script_Null_State_1_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
