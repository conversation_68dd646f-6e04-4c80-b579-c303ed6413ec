// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for Null_State_1
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"
#undef NULL_STATE_1_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Null_State_1
#define UE_TARGET_NAME Null_State_1Editor
#define UE_MODULE_NAME "Null_State_1"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define AIMODULE_API DLLIMPORT
#define NULL_STATE_1_API DLLEXPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define WITH_STATETREE_TRACE 1
#define WITH_STATETREE_TRACE_DEBUGGER 1
#define STATETREEMODULE_API DLLIMPORT
#define PROPERTYBINDINGUTILS_API DLLIMPORT
#define TRACESERVICES_API DLLIMPORT
#define CBOR_API DLLIMPORT
#define TRACEANALYSIS_API DLLIMPORT
#define GAMEPLAYSTATETREEMODULE_API DLLIMPORT
