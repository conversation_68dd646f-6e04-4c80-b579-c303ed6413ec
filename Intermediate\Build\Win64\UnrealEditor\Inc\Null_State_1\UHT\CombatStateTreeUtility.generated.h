// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Variant_Combat/AI/CombatStateTreeUtility.h"

#ifdef NULL_STATE_1_CombatStateTreeUtility_generated_h
#error "CombatStateTreeUtility.generated.h already included, missing '#pragma once' in CombatStateTreeUtility.h"
#endif
#define NULL_STATE_1_CombatStateTreeUtility_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FStateTreeCharacterGroundedConditionInstanceData ******************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_21_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeCharacterGroundedConditionInstanceData_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct();


struct FStateTreeCharacterGroundedConditionInstanceData;
// ********** End ScriptStruct FStateTreeCharacterGroundedConditionInstanceData ********************

// ********** Begin ScriptStruct FStateTreeCharacterGroundedCondition ******************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_39_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeCharacterGroundedCondition_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeConditionCommonBase Super;


struct FStateTreeCharacterGroundedCondition;
// ********** End ScriptStruct FStateTreeCharacterGroundedCondition ********************************

// ********** Begin ScriptStruct FStateTreeAttackInstanceData **************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_67_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeAttackInstanceData_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct();


struct FStateTreeAttackInstanceData;
// ********** End ScriptStruct FStateTreeAttackInstanceData ****************************************

// ********** Begin ScriptStruct FStateTreeComboAttackTask *****************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_80_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeComboAttackTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeComboAttackTask;
// ********** End ScriptStruct FStateTreeComboAttackTask *******************************************

// ********** Begin ScriptStruct FStateTreeChargedAttackTask ***************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeChargedAttackTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeChargedAttackTask;
// ********** End ScriptStruct FStateTreeChargedAttackTask *****************************************

// ********** Begin ScriptStruct FStateTreeWaitForLandingTask **************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_126_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeWaitForLandingTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeWaitForLandingTask;
// ********** End ScriptStruct FStateTreeWaitForLandingTask ****************************************

// ********** Begin ScriptStruct FStateTreeFaceActorInstanceData ***********************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_151_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeFaceActorInstanceData_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct();


struct FStateTreeFaceActorInstanceData;
// ********** End ScriptStruct FStateTreeFaceActorInstanceData *************************************

// ********** Begin ScriptStruct FStateTreeFaceActorTask *******************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_168_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeFaceActorTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeFaceActorTask;
// ********** End ScriptStruct FStateTreeFaceActorTask *********************************************

// ********** Begin ScriptStruct FStateTreeFaceLocationInstanceData ********************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_193_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeFaceLocationInstanceData_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct();


struct FStateTreeFaceLocationInstanceData;
// ********** End ScriptStruct FStateTreeFaceLocationInstanceData **********************************

// ********** Begin ScriptStruct FStateTreeFaceLocationTask ****************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_210_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeFaceLocationTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeFaceLocationTask;
// ********** End ScriptStruct FStateTreeFaceLocationTask ******************************************

// ********** Begin ScriptStruct FStateTreeSetCharacterSpeedInstanceData ***************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_235_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeSetCharacterSpeedInstanceData_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct();


struct FStateTreeSetCharacterSpeedInstanceData;
// ********** End ScriptStruct FStateTreeSetCharacterSpeedInstanceData *****************************

// ********** Begin ScriptStruct FStateTreeSetCharacterSpeedTask ***********************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_252_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeSetCharacterSpeedTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeSetCharacterSpeedTask;
// ********** End ScriptStruct FStateTreeSetCharacterSpeedTask *************************************

// ********** Begin ScriptStruct FStateTreeGetPlayerInfoInstanceData *******************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_274_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeGetPlayerInfoInstanceData_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct();


struct FStateTreeGetPlayerInfoInstanceData;
// ********** End ScriptStruct FStateTreeGetPlayerInfoInstanceData *********************************

// ********** Begin ScriptStruct FStateTreeGetPlayerInfoTask ***************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h_299_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStateTreeGetPlayerInfoTask_Statics; \
	NULL_STATE_1_API static class UScriptStruct* StaticStruct(); \
	typedef FStateTreeTaskCommonBase Super;


struct FStateTreeGetPlayerInfoTask;
// ********** End ScriptStruct FStateTreeGetPlayerInfoTask *****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_CombatStateTreeUtility_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
