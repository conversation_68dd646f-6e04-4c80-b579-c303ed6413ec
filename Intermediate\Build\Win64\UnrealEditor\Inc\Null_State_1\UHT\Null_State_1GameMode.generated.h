// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Null_State_1GameMode.h"

#ifdef NULL_STATE_1_Null_State_1GameMode_generated_h
#error "Null_State_1GameMode.generated.h already included, missing '#pragma once' in Null_State_1GameMode.h"
#endif
#define NULL_STATE_1_Null_State_1GameMode_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class ANull_State_1GameMode ****************************************************
NULL_STATE_1_API UClass* Z_Construct_UClass_ANull_State_1GameMode_NoRegister();

#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesANull_State_1GameMode(); \
	friend struct Z_Construct_UClass_ANull_State_1GameMode_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend NULL_STATE_1_API UClass* Z_Construct_UClass_ANull_State_1GameMode_NoRegister(); \
public: \
	DECLARE_CLASS2(ANull_State_1GameMode, AGameModeBase, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Transient | CLASS_Config), CASTCLASS_None, TEXT("/Script/Null_State_1"), Z_Construct_UClass_ANull_State_1GameMode_NoRegister) \
	DECLARE_SERIALIZER(ANull_State_1GameMode)


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h_15_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ANull_State_1GameMode(ANull_State_1GameMode&&) = delete; \
	ANull_State_1GameMode(const ANull_State_1GameMode&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ANull_State_1GameMode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ANull_State_1GameMode); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(ANull_State_1GameMode) \
	NO_API virtual ~ANull_State_1GameMode();


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h_12_PROLOG
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h_15_INCLASS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ANull_State_1GameMode;

// ********** End Class ANull_State_1GameMode ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Null_State_1GameMode_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
