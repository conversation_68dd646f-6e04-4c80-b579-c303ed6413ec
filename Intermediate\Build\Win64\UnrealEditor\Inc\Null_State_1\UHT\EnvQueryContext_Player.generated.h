// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Variant_Combat/AI/EnvQueryContext_Player.h"

#ifdef NULL_STATE_1_EnvQueryContext_Player_generated_h
#error "EnvQueryContext_Player.generated.h already included, missing '#pragma once' in EnvQueryContext_Player.h"
#endif
#define NULL_STATE_1_EnvQueryContext_Player_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UEnvQueryContext_Player **************************************************
NULL_STATE_1_API UClass* Z_Construct_UClass_UEnvQueryContext_Player_NoRegister();

#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_EnvQueryContext_Player_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUEnvQueryContext_Player(); \
	friend struct Z_Construct_UClass_UEnvQueryContext_Player_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend NULL_STATE_1_API UClass* Z_Construct_UClass_UEnvQueryContext_Player_NoRegister(); \
public: \
	DECLARE_CLASS2(UEnvQueryContext_Player, UEnvQueryContext, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/Null_State_1"), Z_Construct_UClass_UEnvQueryContext_Player_NoRegister) \
	DECLARE_SERIALIZER(UEnvQueryContext_Player)


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_EnvQueryContext_Player_h_16_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UEnvQueryContext_Player(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UEnvQueryContext_Player(UEnvQueryContext_Player&&) = delete; \
	UEnvQueryContext_Player(const UEnvQueryContext_Player&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UEnvQueryContext_Player); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UEnvQueryContext_Player); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UEnvQueryContext_Player) \
	NO_API virtual ~UEnvQueryContext_Player();


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_EnvQueryContext_Player_h_13_PROLOG
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_EnvQueryContext_Player_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_EnvQueryContext_Player_h_16_INCLASS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_EnvQueryContext_Player_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UEnvQueryContext_Player;

// ********** End Class UEnvQueryContext_Player ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_Combat_AI_EnvQueryContext_Player_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
