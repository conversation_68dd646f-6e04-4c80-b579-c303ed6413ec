// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Variant_SideScrolling/Gameplay/SideScrollingSoftPlatform.h"

#ifdef NULL_STATE_1_SideScrollingSoftPlatform_generated_h
#error "SideScrollingSoftPlatform.generated.h already included, missing '#pragma once' in SideScrollingSoftPlatform.h"
#endif
#define NULL_STATE_1_SideScrollingSoftPlatform_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UPrimitiveComponent;
struct FHitResult;

// ********** Begin Class ASideScrollingSoftPlatform ***********************************************
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnSoftCollisionOverlap);


NULL_STATE_1_API UClass* Z_Construct_UClass_ASideScrollingSoftPlatform_NoRegister();

#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_19_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesASideScrollingSoftPlatform(); \
	friend struct Z_Construct_UClass_ASideScrollingSoftPlatform_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend NULL_STATE_1_API UClass* Z_Construct_UClass_ASideScrollingSoftPlatform_NoRegister(); \
public: \
	DECLARE_CLASS2(ASideScrollingSoftPlatform, AActor, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Config), CASTCLASS_None, TEXT("/Script/Null_State_1"), Z_Construct_UClass_ASideScrollingSoftPlatform_NoRegister) \
	DECLARE_SERIALIZER(ASideScrollingSoftPlatform)


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_19_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ASideScrollingSoftPlatform(ASideScrollingSoftPlatform&&) = delete; \
	ASideScrollingSoftPlatform(const ASideScrollingSoftPlatform&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ASideScrollingSoftPlatform); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ASideScrollingSoftPlatform); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(ASideScrollingSoftPlatform) \
	NO_API virtual ~ASideScrollingSoftPlatform();


#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_16_PROLOG
#define FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_19_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_19_INCLASS_NO_PURE_DECLS \
	FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h_19_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ASideScrollingSoftPlatform;

// ********** End Class ASideScrollingSoftPlatform *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_hound_OneDrive_Documents_Unreal_Projects_Null_State_1_Source_Null_State_1_Variant_SideScrolling_Gameplay_SideScrollingSoftPlatform_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
