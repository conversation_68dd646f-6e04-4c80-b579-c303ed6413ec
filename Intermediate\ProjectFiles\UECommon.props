<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <Keyword>MakeFileProj</Keyword>
    <PlatformToolset>v143</PlatformToolset>
    <MinimumVisualStudioVersion>17.0</MinimumVisualStudioVersion>
    <VCProjectVersion>17.0</VCProjectVersion>
    <NMakeUseOemCodePage>true</NMakeUseOemCodePage>
    <TargetRuntime>Native</TargetRuntime>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <PropertyGroup>
    <BuildBatchScript>"C:\Program Files\Epic Games\UE_5.6\Engine\Build\BatchFiles\Build.bat"</BuildBatchScript>
    <RebuildBatchScript>"C:\Program Files\Epic Games\UE_5.6\Engine\Build\BatchFiles\Rebuild.bat"</RebuildBatchScript>
    <CleanBatchScript>"C:\Program Files\Epic Games\UE_5.6\Engine\Build\BatchFiles\Clean.bat"</CleanBatchScript>
    <NMakeBuildCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>echo The selected platform/configuration is not valid for this target.</NMakeCleanCommandLine>
    <NMakeOutput>Invalid Output</NMakeOutput>
    <OutDir>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Unused\</OutDir>
    <IntDir>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Unused\</IntDir>
    <IncludePath />
    <ReferencePath />
    <LibraryPath />
    <LibraryWPath />
    <SourcePath />
    <ExcludePath />
    <DefaultSystemIncludePaths>C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\INCLUDE;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt;</DefaultSystemIncludePaths>
  </PropertyGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
</Project>
